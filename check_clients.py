from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import openpyxl
import time
from multiprocessing import Process, Queue, freeze_support
from datetime import datetime, timedelta

# ========= Function run by each browser =========
def process_numbers(numbers_with_rows, process_id, queue):
    service = Service(executable_path="msedgedriver.exe")
    options = webdriver.EdgeOptions()
    options.add_argument("--start-maximized")
    driver = webdriver.Edge(service=service, options=options)

    try:
        print(f"[Browser-{process_id}] Logging in...")
        driver.get("https://app.iclasspro.com/a/eliteegypt/")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.ID, "uname"))).send_keys("soloxo")
        driver.find_element(By.ID, "passwd").send_keys("ISoloXO-2002")
        driver.find_element(By.ID, "loginbutton").click()
        WebDriverWait(driver, 120).until(EC.url_contains("#!/dashboard"))
        print(f"[Browser-{process_id}] Logged in successfully.")
    except Exception as e:
        print(f"[Browser-{process_id}] Login failed:", e)
        driver.quit()
        return

    try:
        driver.get("https://app.iclasspro.com/a/eliteegypt/#!/families")
        time.sleep(3)
        search_box = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="search-input"] | /html/body/div[4]/div/div[1]/aside/div/div[2]/div[1]/input[1]'))
        )
    except Exception as e:
        print(f"[Browser-{process_id}] Failed to access Families page:", e)
        driver.quit()
        return

    for row_index, number in numbers_with_rows:
        print(f"[Browser-{process_id}] Searching: {number}")
        try:
            search_box.clear()
            time.sleep(1)
            search_box.send_keys(number)
            time.sleep(1)
            search_box.send_keys(Keys.RETURN)
            time.sleep(1)

            if driver.find_elements(By.XPATH, '//*[contains(text(), "No families found")]'):
                result = "Not Registered"
            else:
                result = "Registered"

            print(f"[Browser-{process_id}] Result: {number} → {result}")

        except Exception as e:
            print(f"[Browser-{process_id}] Error searching {number}:", e)
            result = "Error"

        queue.put((row_index, result))
        time.sleep(2)

    driver.quit()
    print(f"[Browser-{process_id}] Finished.")


# ========= Write results to Excel in real time + live progress =========
def excel_writer(queue, total_tasks, result_filename):
    workbook = openpyxl.load_workbook("clients.xlsx")
    sheet = workbook.active
    written = 0
    start_time = time.time()

    while written < total_tasks:
        row_index, result = queue.get()
        sheet.cell(row=row_index, column=2).value = result
        workbook.save(result_filename)

        written += 1
        remaining = total_tasks - written
        elapsed = time.time() - start_time
        avg_time = elapsed / written if written else 0
        remaining_time = avg_time * remaining
        eta = datetime.now() + timedelta(seconds=remaining_time)

        print(
            f"[Writer] ✅ {written}/{total_tasks} done | 🔄 {remaining} left | "
            f"⏳ ETA: {int(remaining_time)}s ≈ {eta.strftime('%H:%M:%S')}"
        )

    print(f"\n✅ All results saved to {result_filename}")


# ========= Split a list into N parts =========
def split_list(lst, n):
    k, m = divmod(len(lst), n)
    return [lst[i * k + min(i, m):(i + 1) * k + min(i + 1, m)] for i in range(n)]


if __name__ == '__main__':
    freeze_support()

    # Load numbers with row indexes
    workbook = openpyxl.load_workbook("clients.xlsx")
    sheet = workbook.active
    numbers_with_rows = [
        (row, str(sheet.cell(row=row, column=1).value).strip())
        for row in range(2, sheet.max_row + 1)
        if sheet.cell(row=row, column=1).value
    ]
    total_clients = len(numbers_with_rows)

    # Ask user for number of browsers
    num_browsers = int(input("How many browsers do you want to run in parallel? "))

    # Split data between browsers
    divided_numbers = split_list(numbers_with_rows, num_browsers)

    # Create output filename
    today_str = datetime.now().strftime("%Y_%m_%d")
    result_filename = f"result_{today_str}_{total_clients}.xlsx"

    # Start writer process
    result_queue = Queue()
    writer_process = Process(target=excel_writer, args=(result_queue, total_clients, result_filename))
    writer_process.start()

    # Start browser processes
    browser_processes = []
    for i in range(num_browsers):
        p = Process(target=process_numbers, args=(divided_numbers[i], i+1, result_queue))
        browser_processes.append(p)
        p.start()

    # Wait for all to finish
    for p in browser_processes:
        p.join()

    writer_process.join()
    print(f"✅ All browsers completed. Final Excel file: {result_filename}")
