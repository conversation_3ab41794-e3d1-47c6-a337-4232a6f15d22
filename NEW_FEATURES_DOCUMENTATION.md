# New Data Extraction Features - Documentation

## Overview
The `ads_numbers_updater.py` script has been enhanced to extract two additional data fields for each family record:

1. **Family Active Status** - Determines if the family has any active enrollments
2. **Family Balance Status** - Determines the family's financial balance status

## New Excel Output Structure

The Excel file now contains **4 columns** instead of 2:

| Column | Field Name | Description |
|--------|------------|-------------|
| A | Phone Number | The phone number being searched |
| B | Status | Registration status (Registered/Not Registered/Error) |
| C | Family Active Status | Enrollment status (Active/Inactive/N/A/Error/Unknown) |
| D | Family Balance Status | Financial balance (Positive/Negative/Zero/N/A/Error/Unknown) |

## Data Extraction Logic

### Family Active Status Detection

The script looks for specific HTML elements to determine enrollment status:

**Active Family (has active enrollments):**
- HTML: `<img class="action-icon occupied-enrollments" ...>`
- Result: `"Active"`

**Inactive Family (no active enrollments):**
- HTML: `<img class="action-icon enrollments" ...>`
- Result: `"Inactive"`

**Special Cases:**
- Not Registered families: `"N/A"`
- Error during extraction: `"Error"`
- No matching elements found: `"Unknown"`

### Family Balance Status Detection

The script examines span elements with currency classes:

**Negative Balance (family owes money):**
- HTML: `<span class="span-currency fg-danger">...`
- Result: `"Negative"`

**Positive Balance (family has credit):**
- HTML: `<span class="span-currency fg-success">...`
- Result: `"Positive"`

**Zero Balance (neutral):**
- HTML: `<span class="span-currency ">...` (note the trailing space)
- Result: `"Zero"`

**Special Cases:**
- Not Registered families: `"N/A"`
- Error during extraction: `"Error"`
- No matching elements found: `"Unknown"`

## Technical Implementation

### New Methods Added

1. **`extract_family_active_status(driver)`**
   - Uses CSS selectors and XPath to find enrollment icons
   - Includes fallback mechanisms for reliability
   - Returns string status values

2. **`extract_family_balance_status(driver)`**
   - Uses CSS selectors and XPath to find balance indicators
   - Filters elements by exact class matching
   - Returns string status values

### Modified Methods

1. **`prepare_excel_file(numbers)`**
   - Added columns 3 and 4 headers
   - Maintains backward compatibility

2. **`process_numbers(numbers_with_rows, process_id)`**
   - Calls new extraction methods for registered families
   - Sets "N/A" for unregistered families
   - Enhanced logging with new data

3. **`excel_writer(total_tasks, result_filename)`**
   - Updated to handle 4-value tuples instead of 2-value
   - Writes additional data to columns 3 and 4

## Error Handling

The script includes robust error handling:

- **Network/Page Load Errors**: Returns "Error" for all fields
- **Element Not Found**: Returns "Unknown" for specific fields
- **Parsing Errors**: Gracefully handles unexpected HTML structures
- **Timeout Issues**: Includes small delays for page loading

## Testing Recommendations

1. **Test with Known Data**: Use phone numbers you know the status of
2. **Verify Excel Structure**: Check that 4 columns are created
3. **Check Data Accuracy**: Compare results with manual verification
4. **Test Edge Cases**: Try with unregistered numbers, error cases
5. **Performance Testing**: Monitor if extraction adds significant time

## Troubleshooting

### Common Issues and Solutions

**Issue**: "Unknown" status for registered families
- **Cause**: Page not fully loaded or HTML structure changed
- **Solution**: Increase wait times or update selectors

**Issue**: "Error" status frequently appearing
- **Cause**: Network issues or site changes
- **Solution**: Check internet connection and site accessibility

**Issue**: Excel file missing new columns
- **Cause**: Using old version of script
- **Solution**: Ensure you're running the updated version

### Debugging Tips

1. Check the log output for detailed extraction results
2. Verify the HTML structure hasn't changed on the website
3. Test with a small number of phone numbers first
4. Monitor browser behavior during extraction

## Future Enhancements

Potential improvements for future versions:

1. **Additional Data Fields**: Extract more family information
2. **Better Error Recovery**: Retry mechanisms for failed extractions
3. **Data Validation**: Cross-check extracted data for consistency
4. **Performance Optimization**: Parallel extraction of multiple data points
5. **Configuration Options**: Allow users to enable/disable specific extractions

## Support

If you encounter issues with the new features:

1. Check this documentation first
2. Review the log output for error messages
3. Test with a small sample to isolate issues
4. Verify the website structure hasn't changed
