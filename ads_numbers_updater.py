import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import openpyxl
from datetime import datetime
import os
import queue

class AdsNumbersUpdater:
    def __init__(self, root):
        self.root = root
        self.root.title("Ads Numbers Updater")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        try:
            self.root.iconbitmap('app.ico')
        except:
            pass
        
        self.is_running = False
        self.stop_requested = False
        self.process_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        self.create_widgets()
        
    def create_widgets(self):
        tk.Label(self.root, text="Paste Numbers:").pack(anchor="w", padx=10, pady=(10, 0))
        self.numbers_input = tk.Text(self.root, height=10, undo=True)
        self.numbers_input.pack(fill="both", padx=10, pady=5, expand=True)
        
        # Context menu for right-click
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Copy", command=self.copy_to_clipboard)
        self.context_menu.add_command(label="Cut", command=self.cut_to_clipboard)
        self.context_menu.add_command(label="Paste", command=self.paste_from_clipboard)
        self.context_menu.add_command(label="Undo", command=self.numbers_input.edit_undo)
        self.context_menu.add_command(label="Redo", command=self.numbers_input.edit_redo)
        self.context_menu.add_command(label="Select All", command=self.select_all)
        self.numbers_input.bind("<Button-3>", self.show_context_menu)
        
        # Bind shortcuts using keysym names
        self.root.bind_all("<Control-c>", self.copy_to_clipboard)  # Ctrl+C
        self.root.bind_all("<Control-x>", self.cut_to_clipboard)   # Ctrl+X
        self.root.bind_all("<Control-v>", self.paste_from_clipboard)  # Ctrl+V
        self.root.bind_all("<Control-z>", lambda e: self.numbers_input.edit_undo())  # Ctrl+Z
        self.root.bind_all("<Control-y>", lambda e: self.numbers_input.edit_redo())  # Ctrl+Y
        self.root.bind_all("<Control-a>", self.select_all)  # Ctrl+A
        
        tk.Label(self.root, text="Number of Browsers:").pack(anchor="w", padx=10, pady=(10, 0))
        self.browser_count_input = tk.Entry(self.root)
        self.browser_count_input.insert(0, "3")
        self.browser_count_input.pack(fill="x", padx=10, pady=5)
        
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.start_button = tk.Button(button_frame, text="Start", command=self.start_processing)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(button_frame, text="Stop", command=self.request_stop, state=tk.DISABLED)
        self.stop_button.pack(side="left", padx=5)
        
        about_button = tk.Button(button_frame, text="About", command=self.show_about_window)
        about_button.pack(side="left", padx=5)

        self.progress_bar = ttk.Progressbar(self.root, length=400)
        self.progress_bar.pack(pady=10)
        
        self.status_label = tk.Label(self.root, text="Waiting to start...")
        self.status_label.pack()
        
        self.log_text = tk.Text(self.root, height=8, state=tk.DISABLED)
        self.log_text.pack(fill="both", padx=10, pady=10, expand=True)
        
    def show_context_menu(self, event):
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
            
    def copy_to_clipboard(self, event=None):
        try:
            selected_text = self.numbers_input.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
        except:
            pass
        return "break"
        
    def cut_to_clipboard(self, event=None):
        try:
            selected_text = self.numbers_input.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            self.numbers_input.delete(tk.SEL_FIRST, tk.SEL_LAST)
        except:
            pass
        return "break"
        
    def paste_from_clipboard(self, event=None):
        try:
            clipboard_text = self.root.clipboard_get()
            self.numbers_input.insert(tk.INSERT, clipboard_text)
        except:
            pass
        return "break"
        
    def select_all(self, event=None):
        try:
            self.numbers_input.tag_add("sel", "1.0", "end")
            self.numbers_input.mark_set("insert", "end")
            self.numbers_input.see("insert")
        except:
            pass
        return "break"
        
    def show_about_window(self):
        about = tk.Toplevel(self.root)
        about.title("About - Ads Numbers Updater")
        about.geometry("500x400")
        about.resizable(False, False)
        
        try:
            about.iconbitmap('app.ico')
        except:
            pass

        content = (
            "📌 Program Name: Ads Numbers Updater\n"
            "📦 Version: v1.0\n\n"
            "🔧 Program Overview:\n"
            "This program is a smart desktop solution designed to help users check and verify\n"
            "a list of phone numbers through an automated system.\n\n"
            "💡 Key Functions:\n"
            "- Automatically checks if each number is registered in the system.\n"
            "- Splits the task between multiple browser windows to save time.\n"
            "- Displays a live progress bar and estimated time remaining.\n"
            "- Stores all results in a well-organized file.\n"
            "- Opens the result file automatically once processing is complete.\n"
            "- Supports resuming the process if interrupted.\n\n"
            "👨‍💻 Developer: Eslam Hassan ( SoloXO )\n"
            "📞 Contact: "
        )

        text = tk.Text(about, wrap="word", font=("Segoe UI", 10), height=18)
        text.insert("1.0", content)
        text.configure(state="disabled")
        text.pack(fill="both", padx=10, pady=(10, 0))

        contact_frame = tk.Frame(about)
        contact_frame.pack(pady=5, padx=10, fill="x")
        
        whatsapp_btn = tk.Button(
            contact_frame, 
            text="WhatsApp", 
            fg="green", 
            cursor="hand2",
            command=lambda: os.system("start https://wa.me/201553617112")
        )
        whatsapp_btn.pack(side="left", expand=True, fill="x", padx=2)
        
        instagram_btn = tk.Button(
            contact_frame, 
            text="Instagram", 
            fg="#C13584", 
            cursor="hand2",
            command=lambda: os.system("start https://www.instagram.com/islam.soloxo/")
        )
        instagram_btn.pack(side="left", expand=True, fill="x", padx=2)

        close_btn = tk.Button(about, text="Close", command=about.destroy)
        close_btn.pack(pady=5)
        
    def log_message(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def start_processing(self):
        if self.is_running:
            return
            
        numbers_text = self.numbers_input.get("1.0", tk.END).strip()
        if not numbers_text:
            messagebox.showwarning("Missing Input", "Please paste numbers to process.")
            return

        try:
            browser_count = int(self.browser_count_input.get())
            if browser_count <= 0:
                raise ValueError
        except ValueError:
            messagebox.showwarning("Invalid Input", "Please enter a valid number of browsers.")
            return

        numbers = [n.strip() for n in numbers_text.splitlines() if n.strip()]
        total = len(numbers)
        
        try:
            self.prepare_excel_file(numbers)
        except Exception as e:
            messagebox.showerror("Excel Error", f"Failed to prepare Excel file: {str(e)}")
            return

        self.is_running = True
        self.stop_requested = False
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar["maximum"] = total
        self.progress_bar["value"] = 0
        self.status_label["text"] = f"Processing 0 / {total}..."
        self.log_message("Starting processing...")
        
        self.run_automation(numbers, browser_count, total)
        
    def prepare_excel_file(self, numbers):
        self.workbook = openpyxl.Workbook()
        sheet = self.workbook.active
        sheet.title = "Results"
        
        sheet.cell(row=1, column=1, value="Phone Number")
        sheet.cell(row=1, column=2, value="Status")
        
        for i, number in enumerate(numbers, start=2):
            sheet.cell(row=i, column=1, value=number)
            
        today_str = datetime.now().strftime("%Y_%m_%d")
        self.result_filename = f"result_{today_str}_{len(numbers)}.xlsx"
        self.workbook.save(self.result_filename)
        
    def request_stop(self):
        if self.is_running:
            self.stop_requested = True
            self.stop_button.config(state=tk.DISABLED)
            self.log_message("Stopping after current batch completes...")
            
    def update_progress(self, processed, total, eta):
        self.progress_bar["value"] = processed
        self.status_label["text"] = f"Processing {processed} / {total} | ETA: {int(eta)}s"
        
    def run_automation(self, numbers, browser_count, total):
        numbers_with_rows = [(row, num) for row, num in enumerate(numbers, start=2)]
        divided_numbers = self.split_list(numbers_with_rows, browser_count)
        
        writer_thread = threading.Thread(
            target=self.excel_writer, 
            args=(total, self.result_filename),
            daemon=True
        )
        writer_thread.start()
        
        self.browser_threads = []
        for i in range(browser_count):
            thread = threading.Thread(
                target=self.process_numbers,
                args=(divided_numbers[i], i+1),
                daemon=True
            )
            self.browser_threads.append(thread)
            thread.start()
            time.sleep(1)
            
        monitor_thread = threading.Thread(
            target=self.monitor_threads,
            args=(writer_thread, self.browser_threads),
            daemon=True
        )
        monitor_thread.start()
            
    def process_numbers(self, numbers_with_rows, process_id):
        service = Service(executable_path="msedgedriver.exe")
        options = webdriver.EdgeOptions()
        options.add_argument("--start-maximized")
        driver = webdriver.Edge(service=service, options=options)

        try:
            self.process_queue.put((f"[Browser-{process_id}] Logging in...", "log"))
            driver.get("https://app.iclasspro.com/a/eliteegypt/")
            WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.ID, "uname"))).send_keys("soloxo")
            driver.find_element(By.ID, "passwd").send_keys("ISoloXO-2002")
            driver.find_element(By.ID, "loginbutton").click()
            WebDriverWait(driver, 120).until(EC.url_contains("#!/dashboard"))
            self.process_queue.put((f"[Browser-{process_id}] Logged in successfully.", "log"))
        except Exception as e:
            self.process_queue.put((f"[Browser-{process_id}] Login failed: {str(e)}", "log"))
            driver.quit()
            return

        try:
            driver.get("https://app.iclasspro.com/a/eliteegypt/#!/families")
            time.sleep(3)
            search_box = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="search-input"] | /html/body/div[4]/div/div[1]/aside/div/div[2]/div[1]/input[1]'))
            )
        except Exception as e:
            self.process_queue.put((f"[Browser-{process_id}] Failed to access Families page: {str(e)}", "log"))
            driver.quit()
            return

        for row_index, number in numbers_with_rows:
            if self.stop_requested:
                break
                
            self.process_queue.put((f"[Browser-{process_id}] Searching: {number}", "log"))
            try:
                search_box.clear()
                time.sleep(1)
                search_box.send_keys(number)
                time.sleep(1)
                search_box.send_keys(Keys.RETURN)
                time.sleep(1)

                if driver.find_elements(By.XPATH, '//*[contains(text(), "No families found")]'):
                    result = "Not Registered"
                else:
                    result = "Registered"

                self.process_queue.put((f"[Browser-{process_id}] Result: {number} → {result}", "log"))
                self.result_queue.put((row_index, result))
                
            except Exception as e:
                self.process_queue.put((f"[Browser-{process_id}] Error searching {number}: {str(e)}", "log"))
                self.result_queue.put((row_index, "Error"))

            time.sleep(1)

        driver.quit()
        self.process_queue.put((f"[Browser-{process_id}] Finished.", "log"))
        
    def excel_writer(self, total_tasks, result_filename):
        workbook = openpyxl.load_workbook(result_filename)
        sheet = workbook.active
        written = 0
        start_time = time.time()

        while written < total_tasks and not self.stop_requested:
            try:
                row_index, result = self.result_queue.get(timeout=1)
                sheet.cell(row=row_index, column=2).value = result
                workbook.save(result_filename)
                written += 1
                
                remaining = total_tasks - written
                elapsed = time.time() - start_time
                avg_time = elapsed / written if written else 0
                remaining_time = avg_time * remaining
                
                self.process_queue.put((written, remaining_time, "progress"))
                self.process_queue.put((f"[Writer] ✅ {written}/{total_tasks} done | 🔄 {remaining} left | ⏳ ETA: {int(remaining_time)}s", "log"))
                
            except queue.Empty:
                continue
            except Exception as e:
                self.process_queue.put((f"[Writer] Error: {str(e)}", "log"))
                
        self.process_queue.put((f"\n✅ All results saved to {result_filename}", "log"))
        
    def monitor_threads(self, writer_thread, browser_threads):
        while any(t.is_alive() for t in browser_threads) and not self.stop_requested:
            time.sleep(0.5)
            
        time.sleep(2)
        
        self.is_running = False
        self.root.after(0, self.on_process_complete)
        
    def split_list(self, lst, n):
        k, m = divmod(len(lst), n)
        return [lst[i * k + min(i, m):(i + 1) * k + min(i + 1, m)] for i in range(n)]
        
    def on_process_complete(self):
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        if not self.stop_requested:
            messagebox.showinfo("Done!", "Processing complete.")
            try:
                os.startfile(self.result_filename)
            except:
                self.log_message(f"Could not open file automatically. Please open: {self.result_filename}")
        else:
            messagebox.showinfo("Stopped", "Processing was stopped by user.")
            
    def update_ui_from_queue(self):
        try:
            while True:
                item = self.process_queue.get_nowait()
                
                if isinstance(item, tuple) and len(item) == 3 and item[2] == "progress":
                    processed, eta, _ = item
                    self.update_progress(processed, self.progress_bar["maximum"], eta)
                elif isinstance(item, tuple) and len(item) == 2 and item[1] == "log":
                    message, _ = item
                    self.log_message(message)
                else:
                    self.log_message(str(item))
                    
        except queue.Empty:
            pass
            
        self.root.after(100, self.update_ui_from_queue)

if __name__ == '__main__':
    root = tk.Tk()
    app = AdsNumbersUpdater(root)
    app.update_ui_from_queue()
    root.mainloop()